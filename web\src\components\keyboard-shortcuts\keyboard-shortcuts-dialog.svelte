<script lang="ts">
  import { Dialog as DialogPrimitive } from 'bits-ui';
  import { Search, X } from 'lucide-svelte';
  import * as ScrollArea from '$lib/components/ui/scroll-area';
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import { shortcutGroups } from '$lib/keyboard-shortcuts/shortcuts-registry';

  // Props
  export let open = false;

  let searchQuery = '';

  // Convert shortcut groups to the format expected by the dialog
  $: categories = shortcutGroups.map((group) => ({
    name: group.name,
    shortcuts: group.shortcuts.map((shortcut) => ({
      action: shortcut.action,
      keys: shortcut.keys,
      description: shortcut.description,
    })),
  }));

  // Filter shortcuts based on search query
  $: filteredCategories = searchQuery
    ? categories
        .map((category) => ({
          name: category.name,
          shortcuts: category.shortcuts.filter(
            (shortcut) =>
              shortcut.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
              shortcut.keys.toLowerCase().includes(searchQuery.toLowerCase()) ||
              (shortcut.description &&
                shortcut.description.toLowerCase().includes(searchQuery.toLowerCase()))
          ),
        }))
        .filter((category) => category.shortcuts.length > 0)
    : categories;

  // Active category
  $: activeCategory = categories.length > 0 ? categories[0].name : '';

  // Listen for the toggle-keyboard-shortcuts event
  onMount(() => {
    if (browser) {
      const handleToggleShortcuts = () => {
        open = !open;
      };

      document.addEventListener('toggle-keyboard-shortcuts', handleToggleShortcuts);

      return () => {
        document.removeEventListener('toggle-keyboard-shortcuts', handleToggleShortcuts);
      };
    }
  });
</script>

<DialogPrimitive.Root bind:open>
  <DialogPrimitive.Portal>
    <DialogPrimitive.Overlay />
    <DialogPrimitive.Content class="bg-background">
      <div class="border-border flex items-center justify-between border-b px-6 py-4">
        <DialogPrimitive.Title class="flex items-center text-lg font-semibold">
          Keyboard Shortcuts
        </DialogPrimitive.Title>
        <DialogPrimitive.Close
          class="text-muted-foreground hover:text-foreground rounded-full p-1 opacity-70 transition-opacity hover:opacity-100">
          <X class="h-4 w-4" />
        </DialogPrimitive.Close>
      </div>

      <div class="flex flex-col">
        <div class="border-border bg-muted text-foreground relative border-b px-6 py-2">
          <div class="absolute inset-y-0 left-6 flex items-center">
            <Search class="text-muted-foreground h-4 w-4" />
          </div>
          <input
            type="text"
            placeholder="Search shortcuts"
            bind:value={searchQuery}
            class="placeholder:text-muted-foreground w-full rounded-md py-2 pl-10 pr-4 text-sm focus:outline-none" />
        </div>

        <div class="grid max-h-[60vh] grid-cols-[220px_1fr]">
          <!-- Categories -->
          <div class="border-border overflow-y-auto border-r p-4">
            {#each filteredCategories as category}
              <button
                class="flex w-full flex-row items-center justify-between rounded-md px-3 py-2 text-left text-sm {activeCategory ===
                category.name
                  ? 'bg-primary/10 text-primary font-medium'
                  : 'text-muted-foreground hover:bg-muted/50'}"
                on:click={() => (activeCategory = category.name)}>
                {category.name}
                <span class="text-muted-foreground ml-2 text-xs"
                  >({category.shortcuts.length})</span>
              </button>
            {/each}
          </div>

          <!-- Shortcuts -->
          <ScrollArea.Root class="h-full max-h-[320px] overflow-hidden px-4">
            {#each filteredCategories.filter((c) => c.name === activeCategory || searchQuery) as category}
              <div class="mb-8 mt-4 last:mb-0">
                {#if searchQuery || filteredCategories.length > 1}
                  <h3 class="text-primary mb-3 text-sm font-medium">{category.name}</h3>
                {/if}
                <div class="space-y-4">
                  {#each category.shortcuts as shortcut}
                    <div class="hover:bg-muted/50 flex items-center justify-between rounded-md p-1">
                      <div class="flex flex-col">
                        <span class="text-sm font-medium">{shortcut.action}</span>
                        {#if shortcut.description}
                          <span class="text-muted-foreground text-xs">{shortcut.description}</span>
                        {/if}
                      </div>
                      <div class="ml-4 flex items-center gap-1">
                        {#each shortcut.keys.split('+') as key, i}
                          <kbd
                            class="bg-muted text-muted-foreground border-border min-w-[28px] rounded-md border px-2 py-1 text-center text-xs font-medium shadow-sm">
                            {key}
                          </kbd>
                          {#if i < shortcut.keys.split('+').length - 1}
                            <span class="text-muted-foreground">+</span>
                          {/if}
                        {/each}
                      </div>
                    </div>
                  {/each}
                </div>
              </div>
            {/each}

            {#if filteredCategories.filter((c) => c.name === activeCategory || searchQuery).length === 0}
              <div class="flex h-full flex-col items-center justify-center py-12">
                <Search class="text-muted-foreground mb-4 h-12 w-12 opacity-20" />
                <h3 class="mb-2 text-lg font-medium">No shortcuts found</h3>
                <p class="text-muted-foreground text-sm">Try a different search term</p>
              </div>
            {/if}
          </ScrollArea.Root>
        </div>

        <!-- Footer -->
        <div class="border-border text-muted-foreground border-t px-6 py-4 text-center text-sm">
          <p>
            Press <kbd
              class="bg-muted text-muted-foreground border-border min-w-[28px] rounded-md border px-2 py-0.5 text-center text-xs font-medium shadow-sm"
              >Alt</kbd>
            +
            <kbd
              class="bg-muted text-muted-foreground border-border min-w-[28px] rounded-md border px-2 py-0.5 text-center text-xs font-medium shadow-sm"
              >/</kbd> anywhere in the app to open this dialog
          </p>
          <p class="mt-2 text-xs">
            All shortcuts use Alt+ combinations for consistency across browsers and operating
            systems
          </p>
        </div>
      </div>
    </DialogPrimitive.Content>
  </DialogPrimitive.Portal>
</DialogPrimitive.Root>
